import { T_SECOND } from 'monofile-utilities/lib/consts';
import { reportData } from '../../tracker';
import { ReportActionName } from '../../reportType';

type CreateApptFrom = 'calendar' | 'quickAdd';

type BookNowFrom = 'drawer' | 'advancedEdit';

class ApptReporter {
  private createApptDrawerOpenTime: number | null = null;
  private createApptFrom: CreateApptFrom | null = null;
  private bookNowFrom: BookNowFrom = 'drawer';

  public setCreateApptDrawerOpenTime() {
    this.createApptDrawerOpenTime = Date.now();
  }

  public setCreateApptFrom(from: CreateApptFrom) {
    this.createApptFrom = from;
  }

  public setBookNowFrom(from: BookNowFrom) {
    this.bookNowFrom = from;
  }

  /**
   * 上报打开一个创建预约抽屉到 book now 的持续时间
   */
  public reportCreateApptDuration(params?: { [paramKey: string]: any }) {
    if (this.createApptDrawerOpenTime === null) {
      console.warn('Create appointment drawer open time is not set.');
      return;
    }

    const duration = (Date.now() - this.createApptDrawerOpenTime) / T_SECOND;
    // 继续沿用老的上报字段，然后补充一个 payload
    reportData(ReportActionName.apptDrawerBookNowClick, {
      duration,
      from: this.createApptFrom,
      bookNowFrom: this.bookNowFrom,
      ...params,
    });
    this.createApptDrawerOpenTime = null; // Reset after reporting
  }
}

/**
 * 这个需要是一个单例，因为它有内部依赖的上下文
 */
export const apptReporter = new ApptReporter();
