import { type ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Tooltip, cn } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import dayjs from 'dayjs';
import React from 'react';
import { getMainCareType } from '../../../../../components/PetAndServicePicker/utils/getMainCareType';
import { usePricingEnableUpgradeWithModal } from '../../../../../components/Pricing/pricing.hooks';
import { ApptTestIds } from '../../../../../config/testIds/apptDrawer';
import { selectCanEditTicketPermission, selectCurrentPermissions } from '../../../../../store/business/role.selectors';
import { type ApptDetailDrawerBox } from '../../../../../store/calendarLatest/calendar.boxes';
import { type TicketDetailInfo } from '../../../../../store/calendarLatest/calendar.types';
import { selectCreateOrEditPetIncidentPermission } from '../../../../../store/pet/petIncident.selectors';
import { selectPreAuth } from '../../../../../store/stripe/preAuth.selectors';
import { isNormal } from '../../../../../store/utils/identifier';
import { useBusinessIsWorkingLocation } from '../../../../../utils/BusinessUtil';
import { permissionAlertAsync, permissionAlertsAsync } from '../../../../../utils/message';
import { ReportActionName } from '../../../../../utils/reportType';
import { ExpectDateType } from '../../../../CreateWaitList/types/types';
import { resolveValidDate } from '../../../../CreateWaitList/utils/utils';
import { useInvoiceReinvent } from '../../../../PaymentFlow/hooks/useInvoiceReinvent';
import { AppointmentStatus } from '../../../../TicketDetail/AppointmentStatus';
import { useEditCheckInOut } from '../../../components/ApptCheckInOut/useEditCheckInOut';
import { type ActionItem } from '../../../components/types';
import { apptInfoMapBox } from '../../../store/appt.boxes';
import { matchApptFlowScene } from '../../../store/appt.options';
import { ApptFlowScene } from '../../../store/appt.types';
import { useCancelAppt } from '../ApptInfoPanel/ApptInfoStatusNext/modules/ApptCancel/hooks/useCancelAppt';
import { EditTicketPermissionTipV2 } from '../ApptInfoPanel/EditTicketPermissionTipV2';
import { useBookAgainFromTicket } from './useBookAgainFromTicket';
import { type CancelledMoreActionListProps, useCancelledMoreActionList } from './useCancelledMoreActionList';
import { type UseReviewBoosterAction } from './useReviewBoosterAction';
import { useGetReviewBoosterPreference } from './useReviewBoosterPreference';
import { useTicketActions } from './useTicketActions';

export type TicketMoreActionListProps = {
  ticketId: ApptDetailDrawerBox['ticketId'];
  /**
   * 展示 Advanced Edit 按钮
   * @default true
   */
  showAdvancedEdit?: boolean;
  /**
   * 展示 Edit lodging assignment 按钮
   * @default false
   */
  showEditLodgingAssignment?: boolean;
  groomingCustomerInfoIsDeleted: TicketDetailInfo['groomingCustomerInfo']['isDeleted'];
  waitListMainStaffId: number;
  /** 用于判断该ticket 是否是grooming only */
  serviceItemTypes: ServiceItemType[];
  petIds?: string[];
} & CancelledMoreActionListProps &
  UseReviewBoosterAction &
  Pick<TicketDetailInfo, 'appointmentStatus' | 'waitListId' | 'appointmentDate' | 'customerId'>;

/**
 * @deprecated use useApptMoreActionList instead after invoice reinvent fully launched
 */
export const useTicketMoreActionList = (
  props: TicketMoreActionListProps,
  reviewBoosterAction: ActionItem | undefined,
): ActionItem[] => {
  const {
    ticketId,
    showAdvancedEdit = true,
    showEditLodgingAssignment = false,
    noShowStatus,
    noShow,
    noShowInvoiceId,
    waitListId,
    appointmentDate,
    appointmentStatus,
    groomingCustomerInfoIsDeleted,
    waitListMainStaffId,
    customerId,
    serviceItemTypes,
    petIds = [],
  } = props;
  const store = useStore();

  const {
    go2AdvancedEdit,
    setPrintCardModalVisible,
    setApptToWaitlistModal,
    setEditLodgingAssignmentModalVisible,
    setPetIncidentModalVisible,
    setCancelTicketModalVisible,
  } = useTicketActions(ticketId);
  const bookAgain = useBookAgainFromTicket();
  const { cancelAppt } = useCancelAppt({ appointmentId: Number(ticketId) });
  const { isEnableToNewFlow } = useInvoiceReinvent();

  const [permissions, hasEditTicketPermission, hasCreateOrEditPetIncidentPermission] = useSelector(
    selectCurrentPermissions(),
    selectCanEditTicketPermission(),
    selectCreateOrEditPetIncidentPermission(),
  );
  const cancelActionList = useCancelledMoreActionList({ ticketId, noShowStatus, noShow, noShowInvoiceId });
  const isWorkingLocation = useBusinessIsWorkingLocation();
  const { checkPreventByPricingUpgrade } = usePricingEnableUpgradeWithModal('waitingList');
  const { open: openEditCheckInOut } = useEditCheckInOut(String(ticketId));

  const isActionDisabled = !isWorkingLocation;
  const editLodgingAssignmentAction: ActionItem | undefined = showEditLodgingAssignment
    ? {
        label: 'Edit lodging assignment',
        onClick: async () => setEditLodgingAssignmentModalVisible(true),
        disabled: isActionDisabled,
        id: 'editLodgingAssignment',
      }
    : undefined;

  useGetReviewBoosterPreference();
  if (appointmentStatus === AppointmentStatus.CANCELED) {
    return [editLodgingAssignmentAction, ...cancelActionList].filter(Boolean) as ActionItem[];
  }

  const showWaitingList =
    matchApptFlowScene(ApptFlowScene.WaitList, getMainCareType(serviceItemTypes)) &&
    [AppointmentStatus.UNCONFIRMED, AppointmentStatus.CONFIRMED].includes(appointmentStatus) &&
    !isNormal(waitListId);

  let commonActionList: (ActionItem | undefined)[] = [
    editLodgingAssignmentAction,
    {
      label: 'Book again',
      disabled: groomingCustomerInfoIsDeleted,
      onClick: () => bookAgain(),
      testId: ApptTestIds.ApptMoreBookingAgainBtn,
    },
    {
      label: 'Print',
      onClick: async () => setPrintCardModalVisible(true),
      disabled: isActionDisabled,
      testId: ApptTestIds.ApptMorePrintBtn,
    },
    showWaitingList
      ? {
          label: (
            <div
              className="moe-m-[-5px_-10px] moe-p-[5px_10px] moe-font-normal"
              data-action={ReportActionName.AppointmentToWaitlist}
            >
              To waitlist
            </div>
          ),
          disabled: isActionDisabled,
          onClick: async () => {
            if (checkPreventByPricingUpgrade()) return;
            await permissionAlertsAsync(permissions, ['canAdvancedEditTicket'], false);
            const { isValid, date } = resolveValidDate(appointmentDate);

            setApptToWaitlistModal({
              visible: true,
              form: {
                date: { type: ExpectDateType.Any },
                validTill: isValid ? date : dayjs().add(1, 'year'),
                ...(isNormal(waitListMainStaffId) && { staffId: [waitListMainStaffId] }),
              },
              clientId: customerId,
            });
          },
        }
      : undefined,
    showAdvancedEdit
      ? {
          label: (
            <EditTicketPermissionTipV2 tip={`Please request "edit ticket" permission from the business owner`}>
              <div
                className={cn(
                  'moe-font-normal',
                  hasEditTicketPermission ? '' : 'moe-text-[#ccc] moe-cursor-not-allowed',
                )}
              >
                Advanced edit
              </div>
            </EditTicketPermissionTipV2>
          ),
          onClick: go2AdvancedEdit,
          disabled: !hasEditTicketPermission || isActionDisabled,
          testId: ApptTestIds.ApptMoreAdvanceBtn,
        }
      : undefined,
  ];

  if ([AppointmentStatus.CHECKED_IN, AppointmentStatus.READY].includes(appointmentStatus)) {
    commonActionList.push({
      label: 'Edit check-in time',
      onClick: () => openEditCheckInOut(),
      disabled: isActionDisabled,
    });
  }

  if (appointmentStatus === AppointmentStatus.FINISHED) {
    commonActionList = [
      editLodgingAssignmentAction,
      reviewBoosterAction,
      {
        label: 'Book again',
        onClick: bookAgain,
        disabled: groomingCustomerInfoIsDeleted,
        testId: ApptTestIds.ApptMoreBookingAgainBtn,
      },
      {
        label: 'Print',
        onClick: () => setPrintCardModalVisible(true),
        disabled: isActionDisabled,
        id: 'print',
        testId: ApptTestIds.ApptMorePrintBtn,
      },
      {
        label: 'Edit check-in/out time',
        onClick: () => openEditCheckInOut(),
        disabled: isActionDisabled,
      },
    ].filter(Boolean) as ActionItem[];
  }

  if (hasCreateOrEditPetIncidentPermission) {
    commonActionList.push({
      label: 'Add incident',
      disabled: isActionDisabled,
      onClick: async () => {
        setPetIncidentModalVisible(true, petIds);
      },
      testId: ApptTestIds.ApptMoreAddIncidentBtn,
    });
  }

  const hasCancelPermission = permissions.has('cancelOrDeleteTicket');
  commonActionList.push({
    label: (
      <Tooltip
        side="bottom"
        isDisabled={hasCancelPermission}
        content={`Please request "cancel ticket" permission from the business owner`}
        sideOffset={10}
      >
        <div className={hasCancelPermission ? '!moe-text-[#D0021B]' : '!moe-text-[#ccc]'}>Cancel appointment</div>
      </Tooltip>
    ),
    onClick: async () => {
      await permissionAlertAsync(permissions, 'cancelOrDeleteTicket');
      if (isEnableToNewFlow) {
        cancelAppt({
          apptInfo: store.select(apptInfoMapBox.mustGetItem(String(ticketId))),
          preAuth: store.select(selectPreAuth(ticketId)),
        });
        return;
      }
      setCancelTicketModalVisible(true);
    },
    disabled: !hasCancelPermission || isActionDisabled,
    className: '!moe-text-[#D0021B] !moe-border-0 !moe-border-solid !moe-border-t !moe-border-t-[#E6E6E6]',
    id: 'cancel',
    testId: ApptTestIds.ApptMoreCancelBtn,
  });
  console.log('commonActionList', commonActionList);
  return commonActionList.filter((item) => !!item) as ActionItem[];
};
