import { datadogRum } from '@datadog/browser-rum';
import { useDispatch, useSelector } from 'amos';
import { throttle } from 'lodash';
import React, { memo, useEffect, useCallback, useRef } from 'react';
import ReactDOM from 'react-dom';
import { SwitchBusinessDropdown } from '../../../../components/Business/SwitchBusinessDropdown';
import { useCardVisibleController, useHiddenResourceController } from '../../../../components/CardVisible';
import { Condition } from '../../../../components/Condition';
import { updateLodgingCalendarConfigAndGetApptList } from '../../../../store/calendarLatest/actions/private/lodgingCalendar.actions';
import type { LodgingCalendarConfigModel } from '../../../../store/calendarLatest/lodgingCalendar.boxes';
import { selectCalendarBusinessLodgingUnitList } from '../../../../store/calendarLatest/lodgingCalendar.selectors';
import { useCancelableCallback } from '../../../../utils/hooks/useCancelableCallback';
import { DataDogActionName } from '../../../../utils/logger';
import { LodgingCalendarDate } from './LodgingCalendarDate';
import { LodgingCalendarFilters } from './LodgingCalendarFilters';
import { LodgingCalendarViewType } from './LodgingCalendarViewType';

export interface LodgingCalendarControllerProps {
  onLoading?: (isLoading: boolean) => void;
  onHeightChange?: (height: number) => void;
}

export const LodgingCalendarController = memo(({ onLoading, onHeightChange }: LodgingCalendarControllerProps) => {
  const [businessLodgingUnitList] = useSelector(selectCalendarBusinessLodgingUnitList);
  const containerRef = useRef<HTMLDivElement>(null);

  const dispatch = useDispatch();
  const setHiddenResource = useHiddenResourceController();
  const setVisibleCards = useCardVisibleController();

  const handleUpdateConfigAndGetApptList = useCancelableCallback(
    async (signal, config: Partial<LodgingCalendarConfigModel>) => {
      ReactDOM.unstable_batchedUpdates(() => {
        onLoading?.(true);
        setHiddenResource(true);
        setVisibleCards(undefined);
      });
      try {
        await dispatch(updateLodgingCalendarConfigAndGetApptList(config, signal));
      } finally {
        ReactDOM.unstable_batchedUpdates(() => {
          onLoading?.(false);
          setHiddenResource(false);
          Promise.resolve().then(() => {
            datadogRum.stopDurationVital(DataDogActionName.CHANGE_CALENDAR_DATE);
          });
        });
      }
    },
  );

  const updateHeight = useCallback(
    throttle((height: number) => {
      // `--lodging-controller-height` 是供 FullCalendar 表头使用的，来保证表头完全在 LodgingCalendarController 下方，不重叠
      document.documentElement.style.setProperty('--lodging-controller-height', `${height}px`);
      onHeightChange?.(height);
    }, 100),
    [onHeightChange],
  );

  // 监听容器高度变化
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // 立即设置初始高度
    const initialHeight = container.getBoundingClientRect().height;
    updateHeight(initialHeight);

    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry) {
        const height = entry.contentRect.height;
        updateHeight(height);
      }
    });

    resizeObserver.observe(container);

    return () => {
      resizeObserver.disconnect();
      document.documentElement.style.removeProperty('--lodging-controller-height');
    };
  }, [updateHeight]);

  return (
    <div ref={containerRef} className="moe-sticky moe-top-0 moe-z-[5] moe-bg-white moe-pt-spacing-m">
      <div className="moe-flex moe-gap-y-s moe-flex-wrap moe-items-center moe-justify-between moe-mb-l">
        <div className="moe-flex moe-gap-x-s">
          <Condition if={businessLodgingUnitList.size}>
            <LodgingCalendarViewType
              onChange={(viewType) => handleUpdateConfigAndGetApptList({ calendarViewType: viewType })}
            />
          </Condition>
          <LodgingCalendarDate onChange={(date) => handleUpdateConfigAndGetApptList({ calendarDate: date })} />
          <SwitchBusinessDropdown scene="working" />
        </div>
        <Condition if={businessLodgingUnitList.size}>
          <LodgingCalendarFilters onChange={(filter) => handleUpdateConfigAndGetApptList(filter)} />
        </Condition>
      </div>
    </div>
  );
});
